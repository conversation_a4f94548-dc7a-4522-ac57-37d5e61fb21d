# FTP Deployment Guide for Group Randomizer App

## Quick Start for FileZilla FTP Hosting

### Step 1: Prepare Your Files
Make sure you have these files ready:
- `index.html` (Student join page - NO admin data visible)
- `admin.html` (Admin dashboard)
- `landing.html` (Login page)

### Step 2: FTP Upload Process

#### Using FileZilla Client:
1. **Connect to FTP Server**
   - Host: `your-server-address.com` or IP address
   - Username: `your-ftp-username`
   - Password: `your-ftp-password`
   - Port: `21` (default) or your custom port

2. **Navigate to Web Directory**
   - Common paths: `/public_html/`, `/htdocs/`, `/www/`, or `/html/`
   - This is where your website files should go

3. **Upload Files**
   - Drag and drop all HTML files to the web directory
   - Ensure files are uploaded in **ASCII/Text mode** for HTML files

#### Using Command Line FTP:
```bash
ftp your-server-address.com
# Enter username and password when prompted
cd public_html
put landing.html
put admin.html
put index.html
quit
```

### Step 3: Set File Permissions (if on Linux server)
```bash
chmod 644 *.html
```

### Step 4: Test Your Deployment
1. Open browser and go to: `http://your-domain.com/landing.html`
2. Test admin signup/login
3. Create a test group session
4. Test the student join link

## Important Security Notes

### What's Changed for Student Privacy:
- ✅ **Removed admin panel** from student join page (`index.html`)
- ✅ **No admin data visible** to students
- ✅ **Clean join experience** - students only see group assignment
- ✅ **Secure session handling** - only necessary data in join links

### Join Link Structure:
- **Before**: Contained admin debugging information
- **After**: Only contains essential session data (class name, group count, session ID)

## File Structure After Deployment

```
your-website/
├── landing.html     # Entry point - login/signup
├── admin.html       # Admin dashboard (password protected)
└── index.html       # Student join page (clean, no admin data)
```

## Testing Checklist

- [ ] Landing page loads correctly
- [ ] Admin can sign up/login
- [ ] Admin can create group sessions
- [ ] Join link works for students
- [ ] Students can join groups without seeing admin data
- [ ] Admin can sync and reveal groups
- [ ] Export and print functions work

## Backup Strategy

Since this app uses localStorage:
1. **Regular Exports**: Use the Excel export feature regularly
2. **Session Data**: Copy session data from admin panel for backup
3. **Browser Bookmarks**: Save important join links

## Domain Configuration

### If using a custom domain:
1. Point your domain to the server IP
2. Ensure web server is configured for your domain
3. Test with both `www` and non-`www` versions

### If using IP address:
- Access directly via: `http://your-server-ip/landing.html`

## Troubleshooting FTP Issues

### Connection Problems:
- Check firewall settings (port 21)
- Verify FTP credentials
- Try passive mode if active mode fails

### Upload Issues:
- Use ASCII mode for HTML files
- Check file permissions after upload
- Verify web server is running

### Access Problems:
- Check web server configuration
- Verify document root path
- Test with simple HTML file first

## Performance Tips

1. **Enable Gzip** compression on your web server
2. **Set proper cache headers** for static files
3. **Use CDN** if expecting high traffic
4. **Monitor storage usage** - localStorage has limits

---

**Ready to Deploy!** 🚀

Your Group Randomizer app is now student-privacy friendly and ready for FTP hosting.
