<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Join Group</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 2rem; background: #f4f4f4; min-height: 100vh; margin: 0; display: flex; flex-direction: column; }
    .container { max-width: 600px; margin: auto; background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    .footer { text-align: center; color: #666; margin-top: auto; padding: 1rem; font-size: 0.9rem; }
    input, button { width: 100%; padding: 10px; margin: 10px 0; border-radius: 5px; border: 1px solid #ccc; }
    #groupMessage { font-weight: bold; margin-top: 1rem; }
  </style>
</head>
<body>
  <div class="container">
    <div style="text-align: center; margin-bottom: 1rem;">
      <a href="landing.html" style="color: #667eea; text-decoration: none; font-size: 0.9rem;">← Back to Home</a>
    </div>

    <div id="classInfo" style="background: #e3f2fd; padding: 1rem; border-radius: 8px; margin-bottom: 1.5rem; text-align: center; border-left: 4px solid #2196f3;">
      <h3 style="margin: 0 0 0.5rem 0; color: #1565c0;">Welcome to</h3>
      <h2 id="className" style="margin: 0; color: #0d47a1; font-size: 1.5rem;">Loading...</h2>
    </div>

    <h2>Join a Random Group</h2>
    <label>Your Name:</label>
    <input type="text" id="studentName" placeholder="Enter your full name" />
    <button onclick="joinBalancedGroup()">Join Group</button>
    <p id="groupMessage"></p>


  </div>

  <div class="footer">
    <p>&copy; 2024 SparkOn-Technologies. All rights reserved.</p>
  </div>

  <script>
    let sessionData = null;
    let sessionKey = null;

    // Get session data from URL
    function getSessionFromURL() {
      const urlParams = new URLSearchParams(window.location.search);
      const encodedSession = urlParams.get('session');

      if (encodedSession) {
        try {
          const decodedSession = JSON.parse(atob(encodedSession));
          return decodedSession;
        } catch (e) {
          console.error('Invalid session data in URL');
          return null;
        }
      }
      return null;
    }

    // Initialize page
    window.onload = function() {
      sessionData = getSessionFromURL();

      if (sessionData) {
        document.getElementById("className").textContent = sessionData.className;
        sessionKey = 'session_' + sessionData.id;

        // Initialize groups in localStorage if not exists for this session
        if (!localStorage.getItem(sessionKey)) {
          const groups = {};
          for (let i = 1; i <= sessionData.groupCount; i++) {
            groups[i] = [];
          }
          localStorage.setItem(sessionKey, JSON.stringify(groups));
        }
      } else {
        document.getElementById("className").textContent = "Invalid Session";
        document.getElementById("groupMessage").innerHTML =
          '<span style="color: #e53e3e;">⚠️ Invalid or missing session data. Please use the correct joining link provided by your instructor.</span>';
      }
    }

    function joinBalancedGroup() {
      if (!sessionData || !sessionKey) {
        alert("Invalid session. Please use the correct joining link.");
        return;
      }

      const name = document.getElementById("studentName").value.trim();
      if (!name) {
        alert("Please enter your name.");
        return;
      }

      // Get current groups for this session
      let groups = JSON.parse(localStorage.getItem(sessionKey)) || {};

      // Check if user already joined
      for (const [groupNum, members] of Object.entries(groups)) {
        if (members.includes(name)) {
          document.getElementById("groupMessage").innerHTML =
            `<span style="color: #ff9800;">You are already in Group ${groupNum}!</span>`;
          return;
        }
      }

      // Find group with minimum members (balanced random assignment)
      let minSize = Infinity;
      let candidateGroups = [];

      // First, find the minimum group size
      for (const [groupNum, members] of Object.entries(groups)) {
        if (members.length < minSize) {
          minSize = members.length;
        }
      }

      // Then, collect all groups with minimum size
      for (const [groupNum, members] of Object.entries(groups)) {
        if (members.length === minSize) {
          candidateGroups.push(groupNum);
        }
      }

      // Randomly select from candidate groups for true randomness
      const selectedGroup = candidateGroups[Math.floor(Math.random() * candidateGroups.length)];

      if (!selectedGroup) {
        alert("Groups not set up properly. Contact admin.");
        return;
      }

      // Add student to selected group
      groups[selectedGroup].push(name);
      localStorage.setItem(sessionKey, JSON.stringify(groups));

      // Show success message
      document.getElementById("groupMessage").innerHTML =
        `<span style="color: #28a745;">✅ You have been added to <strong>Group ${selectedGroup}</strong>!<br>Please wait for your instructor to reveal all group members.</span>`;

      // Clear the input
      document.getElementById("studentName").value = "";

      // Disable the button temporarily to prevent double-joining
      const joinButton = document.querySelector('button');
      joinButton.disabled = true;
      joinButton.textContent = "Joined Successfully!";

      setTimeout(() => {
        joinButton.disabled = false;
        joinButton.textContent = "Join Group";
      }, 3000);

    }
  </script>
</body>
</html>
