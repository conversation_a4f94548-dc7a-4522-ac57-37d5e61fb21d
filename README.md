# Group Randomizer Web Application

A simple web-based application for creating and managing student groups with balanced random assignment.

## Features

- **Admin Dashboard**: Create group sessions, manage groups, export to Excel, print group lists
- **Student Join Page**: Students can join groups using a shared link
- **Balanced Assignment**: Automatically assigns students to groups with balanced distribution
- **Mobile Responsive**: Works on all devices
- **No Database Required**: Uses browser localStorage for data storage

## Files Structure

```
group-randomizer-app/
├── index.html          # Student join page
├── admin.html          # Admin dashboard
├── landing.html        # Login/signup page
└── README.md          # This file
```

## Hosting on FileZilla (FTP Server)

### Prerequisites
- FileZilla Server installed and configured
- Web server (Apache, Nginx, or IIS) running on the same machine
- FTP access credentials

### Deployment Steps

1. **Prepare Files**
   - Ensure all HTML files are in the project root directory
   - No additional setup or compilation required

2. **Upload via FTP**
   - Open FileZilla Client
   - Connect to your FTP server using your credentials
   - Navigate to your web server's document root (usually `htdocs`, `www`, or `public_html`)
   - Upload all files:
     - `index.html`
     - `admin.html` 
     - `landing.html`
     - `README.md` (optional)

3. **Set Permissions**
   - Ensure files have read permissions (644)
   - Ensure directories have execute permissions (755)

4. **Access the Application**
   - Open your web browser
   - Navigate to: `http://your-domain.com/landing.html`
   - Or: `http://your-server-ip/landing.html`

### File Permissions (Linux/Unix servers)
```bash
chmod 644 *.html
chmod 755 .
```

## Usage Instructions

### For Administrators
1. Go to `landing.html`
2. Sign up or login as admin
3. Create a new group session
4. Share the generated join link with students
5. Use "Sync Groups" to see student assignments
6. Use "Reveal Groups" to display final group assignments
7. Export to Excel or print as needed

### For Students
1. Click the join link provided by instructor
2. Enter your name
3. Click "Join Group"
4. Wait for instructor to reveal group members

## Technical Notes

- **Browser Compatibility**: Works with all modern browsers (Chrome, Firefox, Safari, Edge)
- **Data Storage**: Uses localStorage (client-side storage)
- **No Server Requirements**: Pure HTML/CSS/JavaScript - no backend needed
- **Security**: Basic authentication using localStorage
- **Responsive Design**: Mobile-friendly interface

## Troubleshooting

### Common Issues
1. **"Invalid Session" Error**: Ensure the join link is complete and not truncated
2. **Groups Not Syncing**: Make sure students are using the same browser/device or use the sync feature
3. **Login Issues**: Clear browser cache and localStorage if needed

### Browser Storage Limits
- localStorage has a 5-10MB limit per domain
- For large classes (500+ students), consider clearing old session data periodically

## Support

For technical support or questions, contact SparkOn-Technologies.

---
© 2024 SparkOn-Technologies. All rights reserved.
